{"id": null, "metadata": {"source": "E:\\my_project\\RAG_PROJECT\\datas\\layout-parser-paper.pdf", "coordinates": {"points": [[371.9449768066406, 327.04437255859375], [371.9449768066406, 654.7468872070312], [1342.5760498046875, 654.7468872070312], [1342.5760498046875, 327.04437255859375]], "system": "PixelSpace", "layout_width": 1700, "layout_height": 2200}, "filetype": "application/pdf", "languages": ["eng"], "page_number": 11, "parent_id": "ab543398222da25b3a9231929162d3a0", "filename": "layout-parser-paper.pdf", "category": "NarrativeText", "element_id": "4b9eddb71426681f2828832312457b67"}, "page_content": "focuses on precision, eﬃciency, and robustness. The target documents may have complicated structures, and may require training multiple layout detection models to achieve the optimal accuracy. Light-weight pipelines are built for relatively simple documents, with an emphasis on development ease, speed and ﬂexibility. Ideally one only needs to use existing resources, and model training should be avoided. Through two exemplar projects, we show how practitioners in both academia and industry can easily build such pipelines using LayoutParser and extract high-quality structured document data for their downstream tasks. The source code for these projects will be publicly available in the LayoutParser community hub.", "type": "Document"}