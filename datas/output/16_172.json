{"id": null, "metadata": {"source": "E:\\my_project\\RAG_PROJECT\\datas\\layout-parser-paper.pdf", "coordinates": {"points": [[377.575, 387.8031311035156], [377.575, 478.6263427734375], [1338.5452041955555, 478.6263427734375], [1338.5452041955555, 387.8031311035156]], "system": "PixelSpace", "layout_width": 1700, "layout_height": 2200}, "filetype": "application/pdf", "languages": ["eng"], "page_number": 16, "parent_id": "0b724f39d88b9738a3ec2f6b3e142527", "filename": "layout-parser-paper.pdf", "category": "NarrativeText", "element_id": "ccbee39b80146738a2b1c3f3a8d9ff90"}, "page_content": "[24] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Brad<PERSON>, J<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, N., <PERSON>, L., et al.: Pytorch: An imperative style, high-performance deep learning library. arXiv preprint arXiv:1912.01703 (2019)", "type": "Document"}