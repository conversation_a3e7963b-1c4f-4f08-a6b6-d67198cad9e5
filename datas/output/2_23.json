{"id": null, "metadata": {"source": "E:\\my_project\\RAG_PROJECT\\datas\\layout-parser-paper.pdf", "coordinates": {"points": [[366.0334167480469, 1718.02880859375], [366.0334167480469, 1850.7174072265625], [1349.3123779296875, 1850.7174072265625], [1349.3123779296875, 1718.02880859375]], "system": "PixelSpace", "layout_width": 1700, "layout_height": 2200}, "links": [{"text": "8", "url": "cite.gardner2018allennlp", "start_index": 138}, {"text": "34", "url": "cite.wolf2019huggingface", "start_index": 141}, {"text": "35", "url": "cite.wu2019detectron2", "start_index": 168}], "filetype": "application/pdf", "languages": ["eng"], "page_number": 2, "parent_id": "6ef4849c6337ce11b28bcca21d06a332", "filename": "layout-parser-paper.pdf", "category": "NarrativeText", "element_id": "09c8926fcb41c422b93b13acf0d515f3"}, "page_content": "LayoutParser is well aligned with recent eﬀorts for improving DL model reusability in other disciplines like natural language processing [8, 34] and com- puter vision [35], but with a focus on unique challenges in DIA. We show LayoutParser can be applied in sophisticated and large-scale digitization projects", "type": "Document"}