{"id": null, "metadata": {"source": "E:\\my_project\\RAG_PROJECT\\datas\\layout-parser-paper.pdf", "coordinates": {"points": [[366.72607421875, 825.2670677777776], [366.72607421875, 1057.4219970703125], [1344.0050048828125, 1057.4219970703125], [1344.0050048828125, 825.2670677777776]], "system": "PixelSpace", "layout_width": 1700, "layout_height": 2200}, "filetype": "application/pdf", "languages": ["eng"], "page_number": 7, "filename": "layout-parser-paper.pdf", "category": "NarrativeText", "element_id": "584148af1f13e8b0f9c3ac96423d85e4"}, "page_content": "Based on Coordinates, we implement the TextBlock class that stores both the positional and extra features of individual layout elements. It also supports specifying the reading orders via setting the parent ﬁeld to the index of the parent object. A Layout class is built that takes in a list of TextBlocks and supports processing the elements in batch. Layout can also be nested to support hierarchical layout structures. They support the same operations and transformations as the Coordinate classes, minimizing both learning and deployment eﬀort.", "type": "Document"}