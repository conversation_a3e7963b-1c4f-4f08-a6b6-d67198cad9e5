{"id": null, "metadata": {"source": "E:\\my_project\\RAG_PROJECT\\datas\\layout-parser-paper.pdf", "coordinates": {"points": [[367.04461669921875, 1453.847623333333], [367.04461669921875, 1847.24560546875], [1339.5902099609375, 1847.24560546875], [1339.5902099609375, 1453.847623333333]], "system": "PixelSpace", "layout_width": 1700, "layout_height": 2200}, "filetype": "application/pdf", "languages": ["eng"], "page_number": 6, "parent_id": "836e9227ef393d8b00369e6300fbba4c", "filename": "layout-parser-paper.pdf", "category": "NarrativeText", "element_id": "601f7d95172984c75de081023ca64c15"}, "page_content": "A critical feature of LayoutParser is the implementation of a series of data structures and operations that can be used to eﬃciently process and manipulate the layout elements. In document image analysis pipelines, various post-processing on the layout analysis model outputs is usually required to obtain the ﬁnal outputs. Traditionally, this requires exporting DL model outputs and then loading the results into other pipelines. All model outputs from LayoutParser will be stored in carefully engineered data types optimized for further processing, which makes it possible to build an end-to-end document digitization pipeline within LayoutParser. There are three key components in the data structure, namely the Coordinate system, the TextBlock, and the Layout. They provide diﬀerent levels of abstraction for the layout data, and a set of APIs are supported for transformations or operations on these classes.", "type": "Document"}