{"id": null, "metadata": {"source": "E:\\my_project\\RAG_PROJECT\\datas\\layout-parser-paper.pdf", "coordinates": {"points": [[381.8802490234375, 1204.7984619140625], [381.8802490234375, 1269.9344482421875], [1338.1129150390625, 1269.9344482421875], [1338.1129150390625, 1204.7984619140625]], "system": "PixelSpace", "layout_width": 1700, "layout_height": 2200}, "links": [{"text": "3", "url": "section.1.3", "start_index": 42}], "filetype": "application/pdf", "languages": ["eng"], "page_number": 2, "parent_id": "6ef4849c6337ce11b28bcca21d06a332", "filename": "layout-parser-paper.pdf", "category": "NarrativeText", "element_id": "4c8c2acf1481244e5c3e6f6b95a2db72"}, "page_content": "1. An oﬀ-the-shelf toolkit for applying DL models for layout detection, character recognition, and other DIA tasks (Section 3)", "type": "Document"}