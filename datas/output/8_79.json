{"id": null, "metadata": {"source": "E:\\my_project\\RAG_PROJECT\\datas\\layout-parser-paper.pdf", "coordinates": {"points": [[373.35, 1186.148193359375], [373.35, 1351.4888916015625], [1335.8427039055555, 1351.4888916015625], [1335.8427039055555, 1186.148193359375]], "system": "PixelSpace", "layout_width": 1700, "layout_height": 2200}, "links": [{"text": "14", "url": "Hfootnote.9", "start_index": 230}, {"text": "38", "url": "cite.zhong2019publaynet", "start_index": 309}, {"text": "25", "url": "cite.pletschacher2010page", "start_index": 334}, {"text": "3 . 5", "url": "subsection.1.3.5", "start_index": 374}], "filetype": "application/pdf", "languages": ["eng"], "page_number": 8, "parent_id": "cab37dd4fa2d6ccd9bc0cf0c6b332a9d", "filename": "layout-parser-paper.pdf", "category": "NarrativeText", "element_id": "3a8f4022614e64b866f21dd7a7b7b7cc"}, "page_content": "The end goal of DIA is to transform the image-based document data into a structured database. LayoutParser supports exporting layout data into diﬀerent formats like JSON, csv, and will add the support for the METS/ALTO XML format 14 . It can also load datasets from layout analysis-speciﬁc formats like COCO [38] and the Page Format [25] for training layout models (Section 3.5).", "type": "Document"}