{"id": null, "metadata": {"source": "E:\\my_project\\RAG_PROJECT\\datas\\layout-parser-paper.pdf", "coordinates": {"points": [[381.64111328125, 453.13116455078125], [381.64111328125, 1060.305419921875], [1329.1763916015625, 1060.305419921875], [1329.1763916015625, 453.13116455078125]], "system": "PixelSpace", "layout_width": 1700, "layout_height": 2200}, "text_as_html": "<table><thead><tr><th>Operation Name</th><th>Description</th></tr></thead><tbody><tr><td>block.pad(top, bottom, right, left)</td><td>Enlarge the current block according to the input</td></tr><tr><td>block.scale(fx, fy)</td><td>Scale the current block given the ratio in x and y direction</td></tr><tr><td>block.shift(dx, dy)</td><td>Move the current block with the shift distane in x and y direction</td></tr><tr><td>blockl. is_in(block2)</td><td>Whether block1 is inside of block2</td></tr><tr><td>blockl. intersect (block2)</td><td>Return the intersection region of blockl and block2. Coordinate type to be determined based on the inputs.</td></tr><tr><td>blockl. union(block2)</td><td>Return the union region of blockl and block2. Coordinate type to be determined based on the inputs.</td></tr><tr><td>blockl. relative_to(block2)</td><td>Convert the absolute coordinates of blockl to relative coordinates to block2</td></tr><tr><td>blockl. condition_on(block2)</td><td>Calculate the absolute coordinates of blockl given the canvas block2’s absolute coordinates</td></tr><tr><td>block. crop_image (image)</td><td>Obtain the image segments in the block region</td></tr></tbody></table>", "filetype": "application/pdf", "languages": ["eng"], "page_number": 8, "parent_id": "3bb37678da59898b7785d0f518a10209", "filename": "layout-parser-paper.pdf", "category": "Table", "element_id": "5ada51662abad6e31e15f4447199ee83"}, "page_content": "Operation Name Description block.pad(top, bottom, right, left) Enlarge the current block according to the input block.scale(fx, fy) Scale the current block given the ratio in x and y direction block.shift(dx, dy) Move the current block with the shift distances in x and y direction block1.is in(block2) Whether block1 is inside of block2 block1.intersect(block2) Return the intersection region of block1 and block2. Coordinate type to be determined based on the inputs. block1.union(block2) Return the union region of block1 and block2. Coordinate type to be determined based on the inputs. block1.relative to(block2) Convert the absolute coordinates of block1 to relative coordinates to block2 block1.condition on(block2) Calculate the absolute coordinates of block1 given the canvas block2’s absolute coordinates block.crop image(image) Obtain the image segments in the block region", "type": "Document"}