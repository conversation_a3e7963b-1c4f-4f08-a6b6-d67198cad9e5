{"id": null, "metadata": {"source": "E:\\my_project\\RAG_PROJECT\\datas\\layout-parser-paper.pdf", "coordinates": {"points": [[366.40679931640625, 773.6105346679688], [366.40679931640625, 1004.2463989257812], [1339.4287109375, 1004.2463989257812], [1339.4287109375, 773.6105346679688]], "system": "PixelSpace", "layout_width": 1700, "layout_height": 2200}, "filetype": "application/pdf", "languages": ["eng"], "page_number": 5, "parent_id": "bb9603875915cf44e225bf8572b0d4be", "filename": "layout-parser-paper.pdf", "category": "NarrativeText", "element_id": "0b9e7697400602ac472eda5564c0f44d"}, "page_content": "layout data structures, which are optimized for eﬃciency and versatility. 3) When necessary, users can employ existing or customized OCR models via the uniﬁed API provided in the OCR module. 4) LayoutParser comes with a set of utility functions for the visualization and storage of the layout data. 5) LayoutParser is also highly customizable, via its integration with functions for layout data annotation and model training. We now provide detailed descriptions for each component.", "type": "Document"}