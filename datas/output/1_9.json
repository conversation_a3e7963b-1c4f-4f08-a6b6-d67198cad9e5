{"id": null, "metadata": {"source": "E:\\my_project\\RAG_PROJECT\\datas\\layout-parser-paper.pdf", "coordinates": {"points": [[449.3838806152344, 940.1391155555556], [449.3838806152344, 1574.5709466666665], [1264.9049072265625, 1574.5709466666665], [1264.9049072265625, 940.1391155555556]], "system": "PixelSpace", "layout_width": 1700, "layout_height": 2200}, "links": [{"text": "https :// layout - parser . github . io", "url": "https://layout-parser.github.io", "start_index": 1472}], "filetype": "application/pdf", "languages": ["eng"], "page_number": 1, "parent_id": "693da13bf21981fa8e5afdd9a1eb42a2", "filename": "layout-parser-paper.pdf", "category": "NarrativeText", "element_id": "cfc957c94fe63c8fd7c7f4bcb56e75a7"}, "page_content": "Abstract. Recent advances in document image analysis (DIA) have been primarily driven by the application of neural networks. Ideally, research outcomes could be easily deployed in production and extended for further investigation. However, various factors like loosely organized codebases and sophisticated model conﬁgurations complicate the easy reuse of im- portant innovations by a wide audience. Though there have been on-going eﬀorts to improve reusability and simplify deep learning (DL) model development in disciplines like natural language processing and computer vision, none of them are optimized for challenges in the domain of DIA. This represents a major gap in the existing toolkit, as DIA is central to academic research across a wide range of disciplines in the social sciences and humanities. This paper introduces LayoutParser, an open-source library for streamlining the usage of DL in DIA research and applica- tions. The core LayoutParser library comes with a set of simple and intuitive interfaces for applying and customizing DL models for layout de- tection, character recognition, and many other document processing tasks. To promote extensibility, LayoutParser also incorporates a community platform for sharing both pre-trained models and full document digiti- zation pipelines. We demonstrate that LayoutParser is helpful for both lightweight and large-scale digitization pipelines in real-word use cases. The library is publicly available at https://layout-parser.github.io.", "type": "Document"}