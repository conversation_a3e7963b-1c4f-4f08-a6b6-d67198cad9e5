{"id": null, "metadata": {"source": "E:\\my_project\\RAG_PROJECT\\datas\\layout-parser-paper.pdf", "coordinates": {"points": [[377.575, 325.7846374511719], [377.575, 386.1557312011719], [1338.5325017955552, 386.1557312011719], [1338.5325017955552, 325.7846374511719]], "system": "PixelSpace", "layout_width": 1700, "layout_height": 2200}, "filetype": "application/pdf", "languages": ["eng"], "page_number": 16, "parent_id": "0b724f39d88b9738a3ec2f6b3e142527", "filename": "layout-parser-paper.pdf", "category": "NarrativeText", "element_id": "4881e57a224d4ff5db0b5f0220dcfad3"}, "page_content": "[23] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>: Automatic diﬀerentiation in pytorch (2017)", "type": "Document"}