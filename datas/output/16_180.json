{"id": null, "metadata": {"source": "E:\\my_project\\RAG_PROJECT\\datas\\layout-parser-paper.pdf", "coordinates": {"points": [[371.54583740234375, 1149.607421875], [371.54583740234375, 1208.380615234375], [1346.189453125, 1208.380615234375], [1346.189453125, 1149.607421875]], "system": "PixelSpace", "layout_width": 1700, "layout_height": 2200}, "filetype": "application/pdf", "languages": ["eng"], "page_number": 16, "parent_id": "0b724f39d88b9738a3ec2f6b3e142527", "filename": "layout-parser-paper.pdf", "category": "NarrativeText", "element_id": "a9309151580b95a8472219b193666349"}, "page_content": "[32] <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>: Olala: Object-level active learning based layout annotation. arXiv preprint arXiv:2010.01762 (2020)", "type": "Document"}