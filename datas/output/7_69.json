{"id": null, "metadata": {"source": "E:\\my_project\\RAG_PROJECT\\datas\\layout-parser-paper.pdf", "coordinates": {"points": [[371.5823974609375, 1151.7474365234375], [371.5823974609375, 1445.9437344444445], [1339.66259765625, 1445.9437344444445], [1339.66259765625, 1151.7474365234375]], "system": "PixelSpace", "layout_width": 1700, "layout_height": 2200}, "filetype": "application/pdf", "languages": ["eng"], "page_number": 7, "parent_id": "43f42bdf3e9e134e56234428d6e392d3", "filename": "layout-parser-paper.pdf", "category": "NarrativeText", "element_id": "b6cabf7775784d99be43ef24a005b667"}, "page_content": "LayoutParser provides a uniﬁed interface for existing OCR tools. Though there are many OCR tools available, they are usually conﬁgured diﬀerently with distinct APIs or protocols for using them. It can be ineﬃcient to add new OCR tools into an existing pipeline, and diﬃcult to make direct comparisons among the available tools to ﬁnd the best option for a particular project. To this end, LayoutParser builds a series of wrappers among existing OCR engines, and provides nearly the same syntax for using them. It supports a plug-and-play style of using OCR engines, making it eﬀortless to switch, evaluate, and compare diﬀerent OCR modules:", "type": "Document"}