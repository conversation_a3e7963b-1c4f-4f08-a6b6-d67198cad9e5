{"id": null, "metadata": {"source": "E:\\my_project\\RAG_PROJECT\\datas\\layout-parser-paper.pdf", "coordinates": {"points": [[377.575, 479.9849548339844], [377.575, 567.7662353515625], [1343.3499755859375, 567.7662353515625], [1343.3499755859375, 479.9849548339844]], "system": "PixelSpace", "layout_width": 1700, "layout_height": 2200}, "filetype": "application/pdf", "languages": ["eng"], "page_number": 16, "parent_id": "0b724f39d88b9738a3ec2f6b3e142527", "filename": "layout-parser-paper.pdf", "category": "NarrativeText", "element_id": "509809302dba5fb08dbd8d1b76abd488"}, "page_content": "[25] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>: The page (page analysis and ground-truth elements) format framework. In: 2010 20th International Conference on Pattern Recognition. pp. 257–260. IEEE (2010)", "type": "Document"}